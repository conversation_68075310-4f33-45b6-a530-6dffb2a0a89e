import { defineConfig } from 'vite'
import dts from 'vite-plugin-dts'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { TDesignResolver } from 'unplugin-vue-components/resolvers';
import {fileURLToPath, URL} from "node:url";



export default defineConfig({
  build: {
    lib: {
      entry: 'src/index.ts',
      formats: ['es'],
      fileName: (format) => `index.${format}.js`,
    },
    sourcemap: false,
    outDir: 'dist',
    rollupOptions: {
      external: ['vue', 'vue-router', 'tdesign-vue-next', '@vueuse/core'],
      output: {
        globals: {
          vue: 'Vue'
        }
      }
    }
  },
  plugins: [
    vue(),
    vueJsx(),
    UnoCSS(),
    dts({
      insertTypesEntry: true,
      include: ['src/**/*', 'env.d.ts'],
      exclude: [
        'src/**/*.spec.ts',
        'src/**/*.test.ts',
        '*.config.*',
        'src/**/styles/index.ts',
        'src/**/*.less'
      ],
      staticImport: true,
      rollupTypes: false,
      tsconfigPath: './tsconfig.app.json',
      copyDtsFiles: false,
      logLevel: 'info'
    }),
    AutoImport({
      resolvers: [TDesignResolver({
        library: 'vue-next'
      })],
    }),
    Components({
      resolvers: [TDesignResolver({
        library: 'vue-next'
      })],
    }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@utils': fileURLToPath(new URL('./src/utils', import.meta.url))
    },
  },
})
