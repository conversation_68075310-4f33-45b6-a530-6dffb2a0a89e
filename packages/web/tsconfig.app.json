{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["src/**/*", "src/**/*.vue", "env.d.ts"], "exclude": ["dist", "node_modules"], "compilerOptions": {"declaration": true, "declarationMap": true, "outDir": "./dist", "moduleResolution": "bundler", "allowImportingTsExtensions": false, "strict": true, "noEmit": false, "jsx": "preserve", "jsxImportSource": "vue", "baseUrl": ".", "paths": {"@utils": ["./src/utils"], "@": ["./src"]}, "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.components.tsbuildinfo"}}