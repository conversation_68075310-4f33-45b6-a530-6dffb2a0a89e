:root {
  // 颜色变量
  // 红色
  --red-1: 255, 236, 232;
  --red-2: 253, 205, 197;
  --red-3: 251, 172, 163;
  --red-4: 249, 137, 129;
  --red-5: 247, 101, 96;
  --red-6: 245, 63, 63;
  --red-7: 203, 39, 45;
  --red-8: 161, 21, 30;
  --red-9: 119, 8, 19;
  --red-10: 77, 0, 10;
  // 灰色
  --gray-1: 247, 248, 250;
  --gray-2: 242, 243, 245;
  --gray-3: 229, 230, 235;
  --gray-4: 201, 205, 212;
  --gray-5: 169, 174, 184;
  --gray-6: 134, 144, 156;
  --gray-7: 107, 119, 133;
  --gray-8: 78, 89, 105;
  --gray-9: 39, 46, 59;
  --gray-10: 29, 33, 41;
  // 绿色
  --green-1: 232, 255, 234;
  --green-2: 175, 240, 181;
  --green-3: 123, 225, 136;
  --green-4: 76, 210, 99;
  --green-5: 35, 195, 67;
  --green-6: 0, 180, 42;
  --green-7: 0, 154, 41;
  --green-8: 0, 128, 38;
  --green-9: 0, 102, 34;
  --green-10: 0, 77, 28;
  // 蓝色
  --blue-1: 0, 26, 77;
  --blue-2: 5, 47, 120;
  --blue-3: 19, 76, 163;
  --blue-4: 41, 113, 207;
  --blue-5: 70, 153, 250;
  --blue-6: 90, 170, 251;
  --blue-7: 125, 193, 252;
  --blue-8: 161, 213, 253;
  --blue-9: 198, 232, 254;
  --blue-10: 234, 248, 255;


  --u-bg-color: #F4F4F4;
  --u-bg-color-2: #FAFBFB;
  --u-bg-color-3: #ffffff;
  --u-bg-color-3-hover: rgb(var(--gray-3), 0.5);
  --u-bg-color-3-active: rgb(var(--gray-3), 0.8);



  --u-blue-7: 13, 66, 210;
  --u-blue-6: 22, 93, 255;
  --u-blue-5: 64, 128, 255;


  --u-wechat-color: #2BA471;


  // 浅/禁用
  --u-color-neutral-1: #F7F8FA;
  // 浅色
  --u-color-neutral-2: #F2F3F5;
  // 一般
  --u-color-neutral-3: #E5E6EC;

  // shadow
  --u-shadow-sm: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
  // 弹窗使用
  --u-shadow-lg: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
  0 3px 6px -4px rgba(0, 0, 0, 0.12),
  0 9px 28px 8px rgba(0, 0, 0, 0.05);

  // 圆角
  --u-radius-small: 4px;
  --u-radius-default: 6px;
  --u-radius-medium: 8px;
  --u-radius-large: 10px;
  --u-radius-circle: 50%;


  // 文本颜色
  --u-text-color: var(--td-font-gray-1);
  --u-text-color-tips: #8A939F;
  --u-text-color-3: rgb(134, 144, 156);
  --u-text-color-hover: rgb(0, 82, 217);
  --u-text-color-primary: var(--td-font-gray-1);      // 色彩-文字-主要
  --u-text-color-secondary: var(--td-font-gray-2);    // 色彩-文字-次要
  --u-text-color-placeholder: var(--td-font-gray-3);  // 色彩-文字-占位符/说明
  --u-text-color-disabled: var(--td-font-gray-4);    // 色彩-文字-禁用
  --u-text-color-anti: #fff;                      // 色彩-文字-反色
  --u-text-color-brand: var(--td-brand-color-7);           // 色彩-文字-品牌
  --u-text-color-link: var(--td-brand-color-8);            // 色彩-文字-链接
  --u-text-color-danger: rgb(var(--red-6)) // 色彩-文字-危险
}
