html[theme-mode="dark"] {

  // 颜色变量
  // 红色
  --red-1: 77, 0, 10;
  --red-2: 119, 6, 17;
  --red-3: 161, 22, 31;
  --red-4: 203, 46, 52;
  --red-5: 245, 78, 78;
  --red-6: 247, 105, 101;
  --red-7: 249, 141, 134;
  --red-8: 251, 176, 167;
  --red-9: 253, 209, 202;
  --red-10: 255, 240, 236;
  // 灰色
  --gray-1: 23, 23, 26;
  --gray-2: 46, 46, 48;
  --gray-3: 72, 72, 73;
  --gray-4: 95, 95, 96;
  --gray-5: 120, 120, 122;
  --gray-6: 146, 146, 147;
  --gray-7: 171, 171, 172;
  --gray-8: 197, 197, 197;
  --gray-9: 223, 223, 223;
  --gray-10: 246, 246, 246;

  --green-1: 0, 77, 28;
  --green-2: 4, 102, 37;
  --green-3: 10, 128, 45;
  --green-4: 18, 154, 55;
  --green-5: 29, 180, 64;
  --green-6: 39, 195, 70;
  --green-7: 80, 210, 102;
  --green-8: 126, 225, 139;
  --green-9: 178, 240, 183;
  --green-10: 235, 255, 236;

  --blue-1: 232, 247, 255;
  --blue-2: 195, 231, 254;
  --blue-3: 159, 212, 253;
  --blue-4: 123, 192, 252;
  --blue-5: 87, 169, 251;
  --blue-6: 52, 145, 250;
  --blue-7: 32, 108, 207;
  --blue-8: 17, 75, 163;
  --blue-9: 6, 48, 120;
  --blue-10: 0, 26, 77;


  --u-bg-color: #303133;
  --u-bg-color-2: #2E2E30;
  --u-bg-color-3: #2a2a2b;
  --u-bg-color-3-hover: rgb(var(--gray-3), 0.65);

  // 浅/禁用
  --u-color-neutral-1: #F7F8FA;
  // 浅色
  --u-color-neutral-2: #484849;
  // 一般
  --u-color-neutral-3: #E5E6EC;

  // 文本颜色
  --u-text-color: var(--td-font-white-1);
  --u-text-color-tips: #8A939F;
  --u-text-color-primary: var(--td-font-white-1);      // 色彩-文字-主要
  --u-text-color-secondary: var(--td-font-white-2);    // 色彩-文字-次要
  --u-text-color-placeholder: var(--td-font-white-3);  // 色彩-文字-占位符/说明
  --u-text-color-disabled: var(--td-font-white-4);    // 色彩-文字-禁用
  --u-text-color-anti: #fff;                      // 色彩-文字-反色
  --u-text-color-brand: var(--td-brand-color-8);           // 色彩-文字-品牌
  --u-text-color-link: var(--td-brand-color-8);            // 色彩-文字-链接
  --u-text-color-danger: rgb(var(--red-6)) // 色彩-文字-危险
}
