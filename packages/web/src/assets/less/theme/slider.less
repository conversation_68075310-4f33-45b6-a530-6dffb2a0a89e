.u-slider {
  &-green {
    &:hover {

    }
    .t-slider__button {
      background: rgb(var(--green-4));
      border-color: rgb(var(--green-4));
    }
    .t-slider__button--dragging, .t-slider__button:hover {
      background-color: rgb(var(--green-5));
      border-color: rgb(var(--green-5));
    }
  }
  &-blue {
    .t-slider__button {
      background: rgb(var(--blue-5));
      border-color: rgb(var(--blue-5));
    }
    .t-slider__button--dragging, .t-slider__button:hover {
      background-color: rgb(var(--blue-6)) !important;
      border-color: rgb(var(--blue-6));
    }
  }
  &-no-fill {
    .t-slider__track {
      background: transparent;
    }
  }

  .t-slider__rail, .t-slider__track {
    height: 8px;
  }
  .t-slider__button {
    transition: all 0.2s linear;
  }
  .t-slider__button--dragging {
    box-shadow: none;
    transform: scale(1.25);
  }
}
