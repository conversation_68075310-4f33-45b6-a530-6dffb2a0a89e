<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { getClassPrefix } from '@utils'
import type { BackTopProps, BackTopEmits, BackTopSlots } from './BackTop'

const props = withDefaults(defineProps<BackTopProps>(), {
  visibleHeight: 200,
  easing: 'quartOut',
  duration: 200,
  size: 'default'
})

const emit = defineEmits<BackTopEmits>()
defineSlots<BackTopSlots>()

const prefixCls = getClassPrefix('back-top')

const visible = ref(false)
const target = ref<HTMLElement>()

const isWindow = computed(() => !props.targetContainer)

// 简化的节流函数
let rafId: number | null = null
const throttleByRaf = (fn: () => void) => {
  const throttled = () => {
    if (rafId !== null) return
    rafId = requestAnimationFrame(() => {
      fn()
      rafId = null
    })
  }
  throttled.cancel = () => {
    if (rafId !== null) {
      cancelAnimationFrame(rafId)
      rafId = null
    }
  }
  return throttled
}

const scrollHandler = throttleByRaf(() => {
  if (target.value) {
    const { visibleHeight } = props
    const scrollTop = isWindow.value ? window.pageYOffset : target.value.scrollTop
    visible.value = scrollTop >= visibleHeight
  }
})

const getContainer = (container: string | HTMLElement) => {
  if (typeof container === 'string') {
    console.log('container: ', document.querySelector(container))
    return document.querySelector(container) as HTMLElement
  }
  return container
}

const on = (element: HTMLElement | Window, event: string, handler: () => void) => {
  element.addEventListener(event, handler)
}

const off = (element: HTMLElement | Window, event: string, handler: () => void) => {
  element.removeEventListener(event, handler)
}

onMounted(() => {
  target.value = isWindow.value
    ? document?.documentElement
    : getContainer(props.targetContainer!)

  if (target.value) {
    on(isWindow.value ? window : target.value, 'scroll', scrollHandler)
    scrollHandler()
  }
})

onUnmounted(() => {
  scrollHandler.cancel()
  if (target.value) {
    off(isWindow.value ? window : target.value, 'scroll', scrollHandler)
  }
})

// 简化的动画函数，不依赖外部库
const scrollToTop = () => {
  if (!target.value) return

  emit('click')

  const startScrollTop = isWindow.value ? window.pageYOffset : target.value.scrollTop
  const { duration } = props
  const startTime = performance.now()

  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / duration, 1)

    // 简单的缓动函数 (easeOut)
    const easeOut = 1 - Math.pow(1 - progress, 3)
    const currentScrollTop = startScrollTop * (1 - easeOut)

    if (isWindow.value) {
      window.scrollTo(0, currentScrollTop)
    } else if (target.value) {
      target.value.scrollTop = currentScrollTop
    }

    if (progress < 1) {
      requestAnimationFrame(animate)
    }
  }

  requestAnimationFrame(animate)
}

const positionStyle = computed(() => {
  const { right = 28, bottom = 50 } = props.position || {};
  return {
    right: `${right}px`,
    bottom: `${bottom}px`,
  };
})
</script>

<template>
  <transition name="fade-in">
    <div
      v-if="visible"
      :class="[
        prefixCls,
        {
          [`${prefixCls}-${size}`]: size !== 'default',
        }
      ]"
      :style="positionStyle"
      @click="scrollToTop"
    >
      <slot>
        <button :class="`${prefixCls}-btn`">
          <svg
            :class="`${prefixCls}-icon`"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"/>
          </svg>
        </button>
      </slot>
    </div>
  </transition>
</template>
