@import '../../../assets/less/variables.less';

@back-top: @{u-prefix}-back-top;

.@{back-top} {
  position: fixed;
  right: 28px;
  bottom: 50px;
  z-index: 100;
  cursor: pointer;
  &-small {
    .u-web-back-top-btn {
      width: 32px;
      height: 32px;
    }
  }
  &-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    color: var(--u-text-color-secondary);
    font-size: 16px;
    background-color: var(--u-bg-color-3);
    border: 1px solid var(--u-color-neutral-3);
    border-radius: var(--u-radius-default);
    outline: none;
    cursor: pointer;
    box-shadow: var(--u-shadow-sm);
    transition: all 0.2s ease;

    &:hover {
      color: var(--u-text-color-primary);
      background-color: var(--u-bg-color-3-hover);
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  &-icon {
    width: 16px;
    height: 16px;
  }
}

// 淡入动画
.fade-in-enter-active,
.fade-in-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-in-enter-from,
.fade-in-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

.fade-in-enter-to,
.fade-in-leave-from {
  opacity: 1;
  transform: translateY(0);
}
