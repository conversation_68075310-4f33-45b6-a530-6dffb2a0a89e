@result-prefix-cls: ~'u-web-result';

// 图标状态样式混合
.icon-status (@status) {
  @color: ~'result-color-icon_@{status}';
  @bg: ~'result-color-icon-bg_@{status}';

  color: @@color;
  background-color: @@bg;
}

.@{result-prefix-cls} {
  box-sizing: border-box;
  width: 100%;
  padding: 48px 24px 24px;

  &-icon {
    margin-bottom: 12px;
    font-size: 48px;
    text-align: center;

    &-tip {
      display: flex;
      width: 72px;
      height: 72px;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      margin: 0 auto;
    }

    // status = null (自定义图标)
    &-custom &-tip {
      font-size: 64px;
      color: var(--u-text-color-secondary);
      width: unset;
      height: unset;
    }

    &-success &-tip {
      color: rgb(var(--green-6));
      background-color: rgba(var(--green-6), 0.1);
    }

    &-error &-tip {
      color: rgb(var(--red-6));
      background-color: rgba(var(--red-6), 0.1);
    }

    &-info &-tip {
      color: rgb(var(--blue-6));
      background-color: rgba(var(--blue-6), 0.1);
    }

    &-warning &-tip {
      color: #ff9500;
      background-color: rgba(255, 149, 0, 0.1);
    }

    &-404,
    &-403,
    &-500 {
      padding-top: 24px;

      .@{result-prefix-cls}-icon-tip {
        width: 240px;
        height: 160px;
        line-height: 160px;
      }
    }
  }

  &-title {
    color: var(--u-text-color-primary);
    font-weight: 600;
    font-size: 14px;
    line-height: 1.5715;
    text-align: center;
    margin-bottom: 4px;
  }

  &-subtitle {
    color: var(--u-text-color-secondary);
    font-size: 14px;
    line-height: 1.5715;
    text-align: center;
    margin-bottom: 8px;
  }

  &-extra {
    margin-top: 16px;
    text-align: center;
  }

  &-content {
    margin-top: 24px;
  }
}
