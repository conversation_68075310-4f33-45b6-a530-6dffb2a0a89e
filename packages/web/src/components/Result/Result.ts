import type { JSX } from "vue/jsx-runtime";

const RESULT_STATUS = [
  'info',
  'success',
  'warning',
  'error',
  '403',
  '404',
  '500',
  null,
] as const;

export type ResultStatus = typeof RESULT_STATUS[number];

export interface ResultProps {
  /**
   * @zh 结果页显示的状态
   * @en The status displayed on the result page
   * @values 'info','success','warning','error','403','404','500', null
   */
  status?: ResultStatus;
  /**
   * @zh 标题内容
   * @en Title
   */
  title?: string;
  /**
   * @zh 子标题内容
   * @en Subtitle
   */
  subtitle?: string;
}


/**
 * Result 组件的插槽类型
 */
export interface ResultSlots {
  /**
   * @zh 图标
   * @en Icon
   */
  icon?(): any;
  /**
   * @zh 标题
   * @en Title
   */
  title?(): any;
  /**
   * @zh 副标题
   * @en Subtitle
   */
  subtitle?(): any;
  /**
   * @zh 操作区
   * @en Extra
   */
  extra?(): any;
  /**
   * @zh 默认插槽
   * @en Default
   */
  default?(): any;
}
