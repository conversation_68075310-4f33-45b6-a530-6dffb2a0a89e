<template>
  <div class="result-error-icon">
    <svg width="240" height="160" viewBox="0 0 240 160" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g opacity="0.8">
        <path d="M120 80C120 124.183 84.1828 160 40 160C-4.18278 160 -40 124.183 -40 80C-40 35.8172 -4.18278 0 40 0C84.1828 0 120 35.8172 120 80Z" fill="var(--u-text-color-placeholder)" opacity="0.1"/>
        <path d="M280 80C280 124.183 244.183 160 200 160C155.817 160 120 124.183 120 80C120 35.8172 155.817 0 200 0C244.183 0 280 35.8172 280 80Z" fill="var(--u-text-color-placeholder)" opacity="0.1"/>
      </g>
      <rect x="60" y="40" width="120" height="80" rx="8" fill="var(--u-bg-color-3)" stroke="var(--u-text-color-placeholder)" stroke-width="2"/>
      <path d="M90 60H150M90 80H150M90 100H130" stroke="var(--u-text-color-placeholder)" stroke-width="2" stroke-linecap="round"/>
      <circle cx="120" cy="80" r="20" fill="none" stroke="rgb(var(--red-6))" stroke-width="3"/>
      <path d="M110 70L130 90M130 70L110 90" stroke="rgb(var(--red-6))" stroke-width="3" stroke-linecap="round"/>
    </svg>
    <div class="result-error-text">403</div>
  </div>
</template>

<style scoped lang="less">
.result-error-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.result-error-text {
  font-size: 48px;
  font-weight: bold;
  color: var(--u-text-color-secondary);
  opacity: 0.6;
}
</style>
