/* 覆盖 TDesign 菜单折叠状态下的 logo 左边距 */
.utools {
  height: 100%;
  .t-default-menu.t-is-collapsed .t-menu__logo > * {
    margin-left: 0;
  }
  .t-menu__logo > * {
    margin-left: 0;
  }
  .t-is-collapsed {
    .t-menu__logo .title {
      display: none;
    }
    .t-menu__logo:not(:empty) {
      --at-apply: justify-center;
      border-bottom: none;
    }
  }
  .t-menu__logo {
    padding: 0px 8px;
  }
  .u-web-left-menu-small .t-default-menu__inner .t-menu{
    padding: 8px 3px;
  }
  .t-default-menu__inner .t-menu {
    padding: 12px 6px;
  }
  .t-default-menu {
    background: var(--u-bg-color);
    .t-menu__item.t-is-active:not(.t-is-opened) {
      background: var(--u-bg-color-2);
    }
    .t-menu__item:hover:not(.t-is-active):not(.t-is-disabled) {
      background: var(--u-bg-color-2);
    }
  }
}
.u-web-utools-left-menu {
  position: relative;
  width: 100%;
  height: 100%;
  .u-web-utools-left-menu-collapse {
    z-index: 888;
    width: 32px;
    height: 32px;
    border-radius: var(--u-radius-circle);
    background-color: var(--u-bg-color-2);
    position: absolute;
    right: -32px;
    padding: 4px;
    top: 8px;
    cursor: pointer;
    color: var(--u-text-color-3);
    transition: all 280ms linear;
    &:hover {
      box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
      color: var(--u-text-color-hover);
    }
  }
}

/* 当左侧菜单折叠时，调整最大宽度 */
.utools .t-is-collapsed ~ * .u-web-utools-main-content-area {
  width: calc(100vw - 52px);
}

.utools .u-web-left-menu-small .t-is-collapsed ~ * .u-web-utools-main-content-area {
  width: calc(100vw - 38px);
}

.u-web-utools-content-wrapper {
  --at-apply: w-full min-h-screen h-full overflow-hidden grid;
  box-sizing: border-box;
  grid-template-rows: 24px calc(100vh - 24px - 15px) 15px;
  grid-template-columns: 1fr;
  background: var(--u-bg-color);
  width: 100%;
  overflow-x: auto;
  .content {
    padding: 4px;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    .content-inner {
      width: 100%;
      --at-apply: h-full overflow-y-auto;
      border-radius: var(--u-radius-medium);
      background-color: var(--u-bg-color-2);
      box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px 0px;
    }
  }
}
