<script setup lang="ts">
import UtoolsBaseLayout from './UtoolsBaseLayout.vue'
import { ref } from 'vue'
import { LeftMenu, type LeftMenuInstance, type LoadRouterType } from '../../Menu'
import { getClassPrefix } from '@utils'
withDefaults(
  defineProps<{
    avatar?: string
    loadRouter?: LoadRouterType
    refreshEventListener?: boolean
    title?: string
    size?: 'default' | 'small'
  }>(),
  {
    title: 'utools demo',
    avatar: 'https://www.u-tools.cn/assets/favicon.png',
    size: 'default',
  },
)
const leftMenuRef = ref<LeftMenuInstance>()
const collapsed = ref(true)
</script>

<template>
  <div class="utools">
    <UtoolsBaseLayout>
      <template #left>
        <div :class="getClassPrefix('utools', 'left-menu')">
          <div
            class="flex items-center justify-center"
            :class="getClassPrefix('utools', 'left-menu', 'collapse')"
            @click="leftMenuRef?.changeCollapsed()"
          >
            <div
              class="w-5 h-5 font-bold"
              :class="collapsed ? 'i-p-expandLeft' : 'i-p-expandRight'"
            />
          </div>
          <LeftMenu ref="leftMenuRef"
            v-bind="{ loadRouter, refreshEventListener, size }"
            v-model:collapsed="collapsed"
            hideOperations
          >
            <template #logo>
              <div class="w-8 h-auto overflow-hidden rounded-full">
                <img class="logo w-full h-full" :src="avatar" />
              </div>
              <div class="title pl-5">{{ title }}</div>
            </template>
          </LeftMenu>
        </div>
      </template>
      <template #default>
        <div :class="getClassPrefix('utools', 'content-wrapper')">
          <div class="flex justify-end h-full pr-3 w-full">
            <slot name="header-tips">
            </slot>
          </div>
          <div class="content">
            <div class="content-inner">
              <router-view />
            </div>
          </div>
          <slot name="footer">
            <div class="flex items-center justify-center" style="font-size: 10px">
              © {{ new Date().getFullYear() }} [xiaou]。保留所有权利
            </div>
          </slot>
        </div>
      </template>
    </UtoolsBaseLayout>
  </div>
</template>
