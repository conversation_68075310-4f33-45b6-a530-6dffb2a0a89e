<script setup lang="ts">
import { getClassPrefix } from '@utils'
</script>

<template>
  <t-layout class="h-screen min-h-0">
    <t-aside width="auto">
      <slot name="left"></slot>
    </t-aside>
    <t-layout class="min-h-0"
              :class="[getClassPrefix('utools', 'main-content')]">
      <t-content class="h-full overflow-hidden"
                 :class="[getClassPrefix('utools', 'main-content-area')]">
        <slot name="default"></slot>
      </t-content>
    </t-layout>
  </t-layout>
</template>

<style scoped lang="less">

</style>
