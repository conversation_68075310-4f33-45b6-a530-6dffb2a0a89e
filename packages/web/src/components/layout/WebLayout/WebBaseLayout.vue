<script setup lang="ts">
import {getClassPrefix} from "@utils";
// 定义插槽类型，提供智能提示

defineSlots<{
  /** 头部插槽 - 用于显示页面头部内容 */
  header?(): any
  /** 左侧插槽 - 用于显示左侧边栏内容 */
  left?(): any
  /** 默认插槽 - 主要内容区域 */
  default?(): any
}>()

</script>

<template>
  <t-layout class="h-screen">
    <t-header v-if="$slots.header">
      <slot name="header"></slot>
    </t-header>
    <t-layout class="min-h-0">
      <t-aside width="auto">
        <slot name="left"></slot>
      </t-aside>
      <t-layout class="min-h-0"
                :class="[getClassPrefix('main-content')]">
        <t-content class="xl:p-4 ut:p-2 p-1 h-full overflow-hidden">
          <slot name="default"></slot>
        </t-content>
      </t-layout>
    </t-layout>
  </t-layout>
</template>
