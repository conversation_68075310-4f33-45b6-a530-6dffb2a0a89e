<script setup lang="ts">
import {
  computed,
  nextTick,
  onMounted,
  ref,
  toRefs,
  watch
} from 'vue';
import { getClassPrefix } from '@utils';
import type { SplitPanelProps, SplitPanelEmits, SplitPanelSlots } from './SplitPanel';

const props = withDefaults(defineProps<SplitPanelProps>(), {
  component: 'div',
  direction: 'horizontal',
  defaultSize: 0.5,
  disabled: false
});

const emit = defineEmits<SplitPanelEmits>();
defineSlots<SplitPanelSlots>();

const { direction, size: propSize, defaultSize, min, max } = toRefs(props);

const triggerSize = ref(0);
const wrapperRef = ref<HTMLDivElement>();
const triggerRef = ref<HTMLDivElement>();

// 生成CSS类名前缀
const prefixCls = getClassPrefix('split-panel');
const triggerPrefixCls = getClassPrefix('split-trigger');

// 创建响应式尺寸状态
const sizeState = ref(defaultSize.value);

// 监听外部size变化
watch(propSize, (newSize) => {
  if (newSize !== undefined) {
    sizeState.value = newSize;
  }
}, { immediate: true });

// 尺寸配置计算
function getSizeConfig(size: number | string) {
  const numberSize = typeof size === 'string' ? parseFloat(size) : size;
  let unit = '';

  if (typeof size === 'number' || String(numberSize) === String(size)) {
    unit = numberSize > 1 ? 'px' : '%';
  } else {
    unit = 'px';
  }

  return {
    size: numberSize,
    unit,
    isPx: unit === 'px',
  };
}

const sizeConfig = computed(() => getSizeConfig(sizeState.value));
const isHorizontal = computed(() => direction.value === 'horizontal');

// CSS类名计算
const classNames = computed(() => [
  prefixCls,
  {
    [`${prefixCls}-horizontal`]: isHorizontal.value,
    [`${prefixCls}-vertical`]: !isHorizontal.value,
  },
]);

const triggerClassNames = computed(() => [
  triggerPrefixCls,
  {
    [`${triggerPrefixCls}-horizontal`]: isHorizontal.value,
    [`${triggerPrefixCls}-vertical`]: !isHorizontal.value,
    [`${triggerPrefixCls}-disabled`]: props.disabled,
  }
]);

// 第一个面板样式计算
const firstPaneStyles = computed(() => {
  const { size: numberSize, unit, isPx } = sizeConfig.value;
  const baseVal = isPx ? numberSize : numberSize * 100;
  return {
    flex: `0 0 calc(${baseVal}${unit} - ${triggerSize.value / 2}px)`,
  };
});

// 获取像素尺寸
function getPxSize({
  size,
  defaultSize = '0px',
  containerSize,
}: {
  size: number | string | undefined;
  defaultSize?: string;
  containerSize: number;
}) {
  const actualSize = size ?? defaultSize;
  const config = getSizeConfig(actualSize);
  if (config.isPx) {
    return config.size;
  }
  return config.size * containerSize;
}

function px2percent(numerator: number | string, denominator: number | string) {
  return parseFloat(numerator as string) / parseFloat(denominator as string);
}

// 拖拽记录
const record: {
  startPageX: number;
  startPageY: number;
  startContainerSize: number;
  startSize: number | string;
} = {
  startPageX: 0,
  startPageY: 0,
  startContainerSize: 0,
  startSize: 0,
};

// 获取容器尺寸
async function getContainerSize(): Promise<number> {
  const getSize = () => {
    return isHorizontal.value
      ? (wrapperRef.value?.clientWidth || 0)
      : (wrapperRef.value?.clientHeight || 0);
  };

  if (!wrapperRef.value || !getSize()) {
    await nextTick();
  }

  return getSize();
}

// 更新尺寸
function updateSize(newPxSize: number, containerSize: number) {
  if (!containerSize) {
    return;
  }

  const newSize = sizeConfig.value.isPx
    ? `${newPxSize}px`
    : px2percent(newPxSize, containerSize);

  if (sizeState.value === newSize) return;

  sizeState.value = newSize;
  emit('update:size', newSize);
}

// 获取合法的像素尺寸
function getLegalPxSize(size: number | string, containerSize: number) {
  const pxSize = getPxSize({
    size,
    containerSize,
  });

  const minPxSize = min.value !== undefined ? getPxSize({
    size: min.value,
    defaultSize: '0px',
    containerSize,
  }) : 0;

  const maxPxSize = max.value !== undefined ? getPxSize({
    size: max.value,
    defaultSize: `${containerSize}px`,
    containerSize,
  }) : containerSize;

  let legalPxSize = pxSize;
  legalPxSize = Math.max(legalPxSize, minPxSize);
  legalPxSize = Math.min(legalPxSize, maxPxSize);

  return legalPxSize;
}

// 获取新的像素尺寸
function getNewPxSize({
  startContainerSize,
  startSize,
  startPosition,
  endPosition,
}: {
  startContainerSize: number;
  startSize: number | string;
  startPosition: number;
  endPosition: number;
}) {
  const startPxSize = getPxSize({
    size: startSize,
    containerSize: startContainerSize,
  });

  return getLegalPxSize(
    `${startPxSize + (endPosition - startPosition)}px`,
    startContainerSize
  );
}

// 拖拽移动中
function onMoving(e: MouseEvent) {
  emit('moving', e);

  const newPxSize = isHorizontal.value
    ? getNewPxSize({
        startContainerSize: record.startContainerSize,
        startSize: record.startSize,
        startPosition: record.startPageX,
        endPosition: e.pageX,
      })
    : getNewPxSize({
        startContainerSize: record.startContainerSize,
        startSize: record.startSize,
        startPosition: record.startPageY,
        endPosition: e.pageY,
      });

  updateSize(newPxSize,record.startContainerSize);
}

// 拖拽结束
function onMovingEnd(e: MouseEvent) {
  window.removeEventListener('mousemove', onMoving);
  window.removeEventListener('mouseup', onMovingEnd);
  window.removeEventListener('contextmenu', onMovingEnd);

  document.body.style.cursor = 'default';
  emit('moveEnd', e);
}

// 拖拽开始
async function onMoveStart(e: MouseEvent) {
  if (props.disabled) return;

  emit('moveStart', e);

  record.startPageX = e.pageX;
  record.startPageY = e.pageY;
  record.startContainerSize = await getContainerSize();
  record.startSize = sizeState.value;

  window.addEventListener('mousemove', onMoving);
  window.addEventListener('mouseup', onMovingEnd);
  window.addEventListener('contextmenu', onMovingEnd);

  document.body.style.cursor = isHorizontal.value
    ? 'col-resize'
    : 'row-resize';
}

// ResizeObserver监听触发器尺寸变化
function onTriggerResize() {
  if (triggerRef.value) {
    const { width, height } = triggerRef.value.getBoundingClientRect();
    triggerSize.value = isHorizontal.value ? (width || 0) : (height || 0);
  }
}

// 组件挂载后初始化
onMounted(async () => {
  const containerSize = await getContainerSize();
  const fixedPxSize = getLegalPxSize(sizeState.value, containerSize);
  updateSize(fixedPxSize, containerSize);

  // 初始化触发器尺寸
  onTriggerResize();

  // 监听窗口大小变化
  const resizeObserver = new ResizeObserver(() => {
    onTriggerResize();
  });

  if (triggerRef.value) {
    resizeObserver.observe(triggerRef.value);
  }

  // 组件卸载时清理
  const cleanup = () => {
    resizeObserver.disconnect();
  };

  return cleanup;
});
</script>

<template>
  <component :is="component" ref="wrapperRef" :class="classNames">
    <!-- 第一个面板 -->
    <div
      :class="[`${prefixCls}-pane`, `${prefixCls}-pane-first`]"
      :style="firstPaneStyles"
    >
      <slot name="first" />
    </div>

    <!-- 拖拽触发器 -->
    <div
      v-if="!disabled"
      ref="triggerRef"
      :class="triggerClassNames"
      @mousedown="onMoveStart"
    >
      <slot name="resize-trigger">
        <div :class="`${triggerPrefixCls}-icon`">
          <slot name="resize-trigger-icon">
            <!-- 默认的拖拽图标 -->
            <div :class="`${triggerPrefixCls}-line`"></div>
            <div :class="`${triggerPrefixCls}-line`"></div>
            <div :class="`${triggerPrefixCls}-line`"></div>
          </slot>
        </div>
      </slot>
    </div>

    <!-- 第二个面板 -->
    <div :class="[`${prefixCls}-pane`, `${prefixCls}-pane-second`]">
      <slot name="second" />
    </div>
  </component>
</template>
