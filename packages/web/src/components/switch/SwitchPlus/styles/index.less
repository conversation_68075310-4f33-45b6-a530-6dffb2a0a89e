@import "../../../../assets/less/variables.less";

@switch-prefix-cls: ~'@{u-prefix}-switch-plus';

// 基础尺寸变量
@switch-size-large: 28px;      // large 尺寸
@switch-size-medium: 24px;     // medium 尺寸
@switch-size-small: 20px;      // small 尺寸
@switch-size-mini: 16px;       // mini 尺寸
@switch-size-dot-large: 20px;  // large 按钮尺寸
@switch-size-dot-medium: 16px; // medium 按钮尺寸
@switch-size-dot-small: 14px;  // small 按钮尺寸
@switch-size-dot-mini: 10px;   // mini 按钮尺寸
@switch-line-size-dot-large: 18px;  // large 线条按钮尺寸
@switch-line-size-dot-medium: 14px; // medium 线条按钮尺寸
@switch-line-size-dot-small: 12px;  // small 线条按钮尺寸
@switch-line-size-dot-mini: 8px;    // mini 线条按钮尺寸
@switch-line-height-bg-line: 4px;

// 宽度变量
@switch-circle-large-width: 44px;   // large 圆形宽度
@switch-circle-medium-width: 38px;  // medium 圆形宽度
@switch-circle-small-width: 32px;   // small 圆形宽度
@switch-circle-mini-width: 26px;    // mini 圆形宽度
@switch-round-large-width: 44px;    // large 圆角宽度
@switch-round-medium-width: 38px;   // medium 圆角宽度
@switch-round-small-width: 32px;    // small 圆角宽度
@switch-round-mini-width: 26px;     // mini 圆角宽度
@switch-line-large-width: 44px;     // large 线条宽度
@switch-line-medium-width: 38px;    // medium 线条宽度
@switch-line-small-width: 32px;     // small 线条宽度
@switch-line-mini-width: 26px;      // mini 线条宽度

// 间距变量
@switch-size-large-gap: ((@switch-size-large - @switch-size-dot-large) / 2);
@switch-size-medium-gap: ((@switch-size-medium - @switch-size-dot-medium) / 2);
@switch-size-small-gap: ((@switch-size-small - @switch-size-dot-small) / 2);
@switch-size-mini-gap: ((@switch-size-mini - @switch-size-dot-mini) / 2);
@switch-size-large-line-gap: ((@switch-size-large - @switch-line-size-dot-large) / 2);
@switch-size-medium-line-gap: ((@switch-size-medium - @switch-line-size-dot-medium) / 2);
@switch-size-small-line-gap: ((@switch-size-small - @switch-line-size-dot-small) / 2);
@switch-size-mini-line-gap: ((@switch-size-mini - @switch-line-size-dot-mini) / 2);

@switch-font-size-text: 12px;

// 颜色变量 - 使用主题库颜色
@switch-color-bg_off: rgb(var(--gray-4));
@switch-color-bg_on: rgb(var(--blue-5));
@switch-color-bg_off_disabled: rgb(var(--gray-3));
@switch-color-bg_on_disabled: rgb(var(--gray-4));
@switch-color-bg_off_loading: rgb(var(--gray-4));
@switch-color-bg_on_loading: rgb(var(--blue-6));

@switch-color-dot-bg: var(--u-bg-color-3);
@switch-color-dot-icon_off: rgb(var(--gray-6));
@switch-color-dot-icon_on: rgb(var(--blue-5));
@switch-color-dot-icon_off_disabled: rgb(var(--gray-5));
@switch-color-dot-icon_on_disabled: rgb(var(--gray-5));
@switch-color-dot-icon_off_loading: rgb(var(--gray-6));
@switch-color-dot-icon_on_loading: rgb(var(--blue-5));

@switch-color-text_off: rgb(var(--gray-6));
@switch-color-text_on: var(--u-bg-color-3);
@switch-color-text_off_disabled: rgb(var(--gray-5));
@switch-color-text_on_disabled: rgb(var(--gray-5));
@switch-color-text_off_loading: rgb(var(--gray-6));
@switch-color-text_on_loading: var(--u-bg-color-3);

@switch-line-color-dot-shadow: var(--u-shadow-sm);

// 过渡动画
@transition-duration-2: 0.2s;
@transition-timing-function-standard: ease;
@radius-small: var(--u-radius-small);

// 加载动画关键帧
@keyframes switch-loading-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 文本滑动动画
.switch-slide-text-enter-from {
  left: -100% !important;
}

.switch-slide-text-enter-to {
  left: 8px !important;
}

.switch-slide-text-enter-active {
  transition: left @transition-duration-2 @transition-timing-function-standard;
}

.switch-slide-text-leave-from {
  left: 100% !important;
}

.switch-slide-text-leave-to {
  left: @switch-size-dot-large + @switch-size-large-gap + 6px !important;
}

.switch-slide-text-leave-active {
  transition: left @transition-duration-2 @transition-timing-function-standard;
}

// 主要样式 (默认为 large 尺寸)
.@{switch-prefix-cls} {
  position: relative;
  box-sizing: border-box;
  min-width: @switch-circle-large-width;
  height: @switch-size-large;
  padding: 0;
  overflow: hidden;
  line-height: @switch-size-large;
  vertical-align: middle;
  background-color: @switch-color-bg_off;
  border: none;
  border-radius: (@switch-size-large / 2);
  outline: none;
  cursor: pointer;
  transition: background-color @transition-duration-2 @transition-timing-function-standard;

  &-handle {
    position: absolute;
    top: @switch-size-large-gap;
    left: @switch-size-large-gap;
    display: flex;
    align-items: center;
    justify-content: center;
    width: @switch-size-dot-large;
    height: @switch-size-dot-large;
    color: @switch-color-dot-icon_off;
    font-size: 12px;
    background-color: @switch-color-dot-bg;
    border-radius: 50%;
    transition: all @transition-duration-2 @transition-timing-function-standard;
    box-shadow: @switch-line-color-dot-shadow;
  }

  &-checked {
    background-color: @switch-color-bg_on;
  }

  &-checked &-handle {
    left: calc(100% - @switch-size-dot-large - @switch-size-large-gap);
    color: @switch-color-dot-icon_on;
  }

  &[disabled] &-handle {
    color: @switch-color-dot-icon_off_disabled;
  }

  &[disabled]&-checked &-handle {
    color: @switch-color-dot-icon_on_disabled;
  }

  &-text-holder {
    margin: 0 8px 0 @switch-size-dot-large + @switch-size-large-gap + 6px;
    font-size: @switch-font-size-text;
    opacity: 0;
  }

  &-text {
    position: absolute;
    top: 0;
    left: @switch-size-dot-large + @switch-size-large-gap + 6px;
    color: @switch-color-text_off;
    font-size: @switch-font-size-text;
  }

  &-checked &-text-holder {
    margin: 0 @switch-size-dot-large + @switch-size-large-gap + 6px 0 8px;
  }

  &-checked &-text {
    left: 8px;
    color: @switch-color-text_on;
  }

  // 禁用状态
  &[disabled] {
    background-color: @switch-color-bg_off_disabled;
    cursor: not-allowed;
  }

  &[disabled] &-text {
    color: @switch-color-text_off_disabled;
  }

  &[disabled]&-checked {
    background-color: @switch-color-bg_on_disabled;
  }

  &[disabled]&-checked &-text {
    color: @switch-color-text_on_disabled;
  }

  // 加载状态
  &-loading {
    background-color: @switch-color-bg_off_loading;
  }

  &-loading &-handle {
    color: @switch-color-dot-icon_off_loading;
  }

  &-loading &-text {
    color: @switch-color-text_off_loading;
  }

  &-loading&-checked {
    background-color: @switch-color-bg_on_loading;
  }

  &-loading&-checked &-handle {
    color: @switch-color-dot-icon_on_loading;
  }

  &-loading&-checked &-text {
    color: @switch-color-text_on_loading;
  }

  // 加载图标样式
  &-loading-icon {
    display: inline-block;
    animation: switch-loading-spin 1s linear infinite;
  }

  // medium 尺寸
  &-medium {
    min-width: @switch-circle-medium-width;
    height: @switch-size-medium;
    line-height: @switch-size-medium;
  }

  &-medium&-checked {
    padding-left: -@switch-size-medium-gap;
  }

  &-medium &-handle {
    top: @switch-size-medium-gap;
    left: @switch-size-medium-gap;
    width: @switch-size-dot-medium;
    height: @switch-size-dot-medium;
    border-radius: (@switch-size-medium / 2);

    &-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.8);
    }
  }

  &-medium&-checked &-handle {
    left: calc(100% - @switch-size-dot-medium - @switch-size-medium-gap);
  }

  &-medium &-text-holder {
    margin: 0 8px 0 @switch-size-dot-medium + @switch-size-medium-gap + 6px;
  }

  &-medium &-text {
    left: @switch-size-dot-medium + @switch-size-medium-gap + 6px;
  }

  &-medium&-checked &-text-holder {
    margin: 0 @switch-size-dot-medium + @switch-size-medium-gap + 6px 0 8px;
  }

  &-medium&-checked &-text {
    left: 8px;
  }

  // 小尺寸
  &-small {
    min-width: @switch-circle-small-width;
    height: @switch-size-small;
    line-height: @switch-size-small;
  }

  &-small&-checked {
    padding-left: -@switch-size-small-gap;
  }

  &-small &-handle {
    top: @switch-size-small-gap;
    left: @switch-size-small-gap;
    width: @switch-size-dot-small;
    height: @switch-size-dot-small;
    border-radius: (@switch-size-small / 2);

    &-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.66667);
    }
  }

  &-small&-checked &-handle {
    left: calc(100% - @switch-size-dot-small - @switch-size-small-gap);
  }

  // mini 尺寸
  &-mini {
    min-width: @switch-circle-mini-width;
    height: @switch-size-mini;
    line-height: @switch-size-mini;
  }

  &-mini&-checked {
    padding-left: -@switch-size-mini-gap;
  }

  &-mini &-handle {
    top: @switch-size-mini-gap;
    left: @switch-size-mini-gap;
    width: @switch-size-dot-mini;
    height: @switch-size-dot-mini;
    border-radius: (@switch-size-mini / 2);

    &-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0.5);
    }
  }

  &-mini&-checked &-handle {
    left: calc(100% - @switch-size-dot-mini - @switch-size-mini-gap);
  }

  &-mini &-text-holder {
    margin: 0 6px 0 @switch-size-dot-mini + @switch-size-mini-gap + 4px;
    font-size: 10px;
  }

  &-mini &-text {
    left: @switch-size-dot-mini + @switch-size-mini-gap + 4px;
    font-size: 10px;
  }

  &-mini&-checked &-text-holder {
    margin: 0 @switch-size-dot-mini + @switch-size-mini-gap + 4px 0 6px;
  }

  &-mini&-checked &-text {
    left: 6px;
  }
}

// 圆角类型
.@{switch-prefix-cls} {
  &-type-round {
    min-width: @switch-round-large-width;
    border-radius: @radius-small;
  }

  &-type-round &-handle {
    border-radius: 2px;
  }

  &-type-round&-medium {
    min-width: @switch-round-medium-width;
    border-radius: var(--u-radius-small);
  }

  &-type-round&-medium &-handle {
    border-radius: 2px;
  }

  &-type-round&-small {
    min-width: @switch-round-small-width;
    height: @switch-size-small;
    line-height: @switch-size-small;
    border-radius: 2px;
  }

  &-type-round&-small &-handle {
    border-radius: 1px;
  }

  &-type-round&-mini {
    min-width: @switch-round-mini-width;
    border-radius: 2px;
  }

  &-type-round&-mini &-handle {
    border-radius: 1px;
  }
}

// 线条类型
.@{switch-prefix-cls} {
  &-type-line {
    min-width: @switch-line-large-width;
    overflow: unset;
    background-color: transparent;

    &::after {
      display: block;
      width: 100%;
      height: @switch-line-height-bg-line;
      background-color: @switch-color-bg_off;
      border-radius: (@switch-line-height-bg-line / 2);
      transition: background-color @transition-duration-2 @transition-timing-function-standard;
      content: '';
    }
  }

  &-type-line &-handle {
    top: @switch-size-large-line-gap;
    left: 0;
    width: @switch-line-size-dot-large;
    height: @switch-line-size-dot-large;
    background-color: @switch-color-dot-bg;
    border-radius: (@switch-line-size-dot-large / 2);
    box-shadow: @switch-line-color-dot-shadow;
  }

  &-type-line&-checked {
    background-color: transparent;

    &::after {
      background-color: @switch-color-bg_on;
    }
  }

  &-type-line&-custom-color {
    --custom-color: @switch-color-bg_off;

    &::after {
      background-color: var(--custom-color);
    }
  }

  &-type-line&-custom-color&-checked {
    --custom-color: @switch-color-bg_on;
  }

  &-type-line&-checked &-handle {
    left: calc(100% - @switch-line-size-dot-large);
  }

  // 线条类型 medium 尺寸
  &-type-line&-medium {
    min-width: @switch-line-medium-width;
  }

  &-type-line&-medium &-handle {
    top: @switch-size-medium-line-gap;
    width: @switch-line-size-dot-medium;
    height: @switch-line-size-dot-medium;
    border-radius: (@switch-line-size-dot-medium / 2);
  }

  &-type-line&-medium&-checked &-handle {
    left: calc(100% - @switch-line-size-dot-medium);
  }

  // 线条类型禁用状态
  &-type-line[disabled] {
    background-color: transparent;
    cursor: not-allowed;

    &::after {
      background-color: @switch-color-bg_off_disabled;
    }
  }

  &-type-line[disabled]&-checked {
    background-color: transparent;

    &::after {
      background-color: @switch-color-bg_on_disabled;
    }
  }

  // 线条类型加载状态
  &-type-line&-loading {
    background-color: transparent;

    &::after {
      background-color: @switch-color-bg_off_loading;
    }
  }

  &-type-line&-loading&-checked {
    background-color: transparent;

    &::after {
      background-color: @switch-color-bg_on_loading;
    }
  }

  &-type-line&-small {
    min-width: @switch-line-small-width;
    height: @switch-size-small;
    line-height: @switch-size-small;
  }

  &-type-line&-small&-checked {
    padding-left: -@switch-size-small-line-gap;
  }

  &-type-line&-small &-handle {
    top: @switch-size-small-line-gap;
    width: @switch-line-size-dot-small;
    height: @switch-line-size-dot-small;
    border-radius: (@switch-size-small / 2);

    &-icon {
      transform: translate(-50%, -50%) scale(1);
    }
  }

  &-type-line&-small&-checked &-handle {
    left: calc(100% - @switch-line-size-dot-small);
  }

  // 线条类型 mini 尺寸
  &-type-line&-mini {
    min-width: @switch-line-mini-width;
  }

  &-type-line&-mini &-handle {
    top: @switch-size-mini-line-gap;
    width: @switch-line-size-dot-mini;
    height: @switch-line-size-dot-mini;
    border-radius: (@switch-line-size-dot-mini / 2);
  }

  &-type-line&-mini&-checked &-handle {
    left: calc(100% - @switch-line-size-dot-mini);
  }
}
