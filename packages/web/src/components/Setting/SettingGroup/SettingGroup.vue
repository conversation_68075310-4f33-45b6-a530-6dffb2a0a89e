<script setup lang="ts">
import {getClassPrefix} from "@utils";

defineProps<{
  title?: string;
}>()

</script>

<template>
  <div :class="[getClassPrefix('setting-group')]">
    <div :class="getClassPrefix('setting-group-header')">
      <div :class="getClassPrefix('setting-group-title')">
        {{ title }}
      </div>
      <div v-if="$slots.desc">
        <slot name="desc" />
      </div>
    </div>
    <div>
      <slot />
    </div>
  </div>
</template>

<style scoped lang="less">

</style>
