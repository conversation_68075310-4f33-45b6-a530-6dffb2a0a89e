import ContextMenu from './ContextMenu.vue';
import ContextMenuItem from './ContextMenuItem.vue';
import ContextMenuGroup from './ContextMenuGroup.vue';
import ContextMenuDivider from './ContextMenuDivider.vue';
import ContextMenuSubmenu from './ContextMenuSubmenu.vue';
import './styles';

export { 
  ContextMenu, 
  ContextMenuItem, 
  ContextMenuGroup, 
  ContextMenuDivider,
  ContextMenuSubmenu
};

// 类型定义
export type TriggerEvent = 'hover' | 'click' | 'focus' | 'context-menu';

export type ContextMenuPosition = 
  | 'top'
  | 'top-left'
  | 'top-right'
  | 'bottom'
  | 'bottom-left'
  | 'bottom-right'
  | 'left'
  | 'left-top'
  | 'left-bottom'
  | 'right'
  | 'right-top'
  | 'right-bottom';

export interface ContextMenuOption {
  label: string;
  value: string | number;
  disabled?: boolean;
  danger?: boolean;
  icon?: string;
}

export interface ContextMenuGroup {
  title: string;
  children: ContextMenuOption[];
}

export default ContextMenu;
