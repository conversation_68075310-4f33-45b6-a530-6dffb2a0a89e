<script setup lang="ts">
import { computed } from 'vue'
import { getBuildCurrentClassPrefix, isEventExist } from '@utils'
import type { PageHeaderEmits, PageHeaderProps, PageHeaderSlots } from './PageHeader'

const props = withDefaults(defineProps<PageHeaderProps>(), {
  title: '',
  subtitle: '',
  size: 'default',
})

const buildClassPrefix = getBuildCurrentClassPrefix('page-header');

const pageHeaderClassList = computed(() => {
  const classList: string[] = [buildClassPrefix()];
  if (props.size !== 'default') {
    classList.push(buildClassPrefix(props.size));
  }
  return classList;
});

defineSlots<PageHeaderSlots>();

defineEmits<PageHeaderEmits>();

const hasBack = computed(() => {
  return isEventExist('onBack')
});

</script>

<template>
  <div :class="pageHeaderClassList">
    <div  class="flex items-center"
          :class="buildClassPrefix('header')">
      <slot v-if="hasBack || $slots.prefix"
            name="prefix">
        <t-button :class="buildClassPrefix('header', 'back')"
                  shape="square"
                  variant="text"
                  @click="$emit('back')">
          <template #icon>
            <t-icon  class="i-p-left"></t-icon>
          </template>
        </t-button>
      </slot>
      <div :class="buildClassPrefix('title')">
        <slot name="title">{{ title }}</slot>
      </div>
      <t-divider :class="buildClassPrefix('divider')"
                 layout="vertical"></t-divider>
      <div :class="buildClassPrefix('subtitle')">
        <slot name="subtitle">{{ subtitle }}</slot>
      </div>
    </div>
    <div v-if="$slots.extra"
         :class="buildClassPrefix('extra')">
      <slot name="extra"></slot>
    </div>
  </div>
</template>
