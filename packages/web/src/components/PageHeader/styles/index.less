@import "../../../assets/less/variables.less";

@page-header: @{u-prefix}-page-header;

.@{page-header}{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px 16px 20px;
  background: var(--u-bg-color-2);
  border-bottom: 1px solid var(--u-color-neutral-2);
  &-small {
    padding: 14px 16px 12px 16px;
    .@{page-header}-title {
      font-size: 16px;
    }
    .@{page-header}-header-back .t-icon  {
      font-size: 18px;
    }
  }
  &-large {
    padding: 20px 24px 20px 24px;
    .@{page-header}-title {
      font-size: 20px;
    }
    .@{page-header}-header-back .t-icon  {
      font-size: 24px;
    }
  }
}
.@{page-header}-header {
  //--at-apply: flex items-center;
  gap: 6px;
}

.@{page-header}-header-back  {
  .t-icon {
    font-size: 20px;
  }
}

.@{page-header}-subtitle {
  color: var(--u-text-color-secondary);
}

.@{page-header}-divider {
  height: 20px;
}

.@{page-header}-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--u-text-color);
  line-height: 1.5;
}

.@{page-header}-extra {
  display: flex;
  align-items: center;
  gap: 16px;
}
