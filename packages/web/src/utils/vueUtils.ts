import { getCurrentInstance } from 'vue'
import { upperFirst } from 'es-toolkit'

const VUE_EVENT_PREFIX = 'on'
// 简化的事件修饰符正则，匹配 Vue 3 支持的修饰符组合
const EVENT_MODIFIERS = ['Capture', 'Passive', 'Once']
const EVENT_MODIFIER_REGEXP = new RegExp(`^(${EVENT_MODIFIERS.join('|')})*$`)

/**
 *
 * 用户是否绑定了指定事件, 如果传多个事件名称, 只要有一个事件绑定后会返回 true <br/>
 * vue3 会将事件自动加 on 前缀 <br/>
 * 如果使用 defineEmits 定义后原生事件会放到 attrs 中而自定义的事件并不会放到 attrs 中, 他会当作 props 放到当前 vNode 中 。 <br/>
 * @param eventNameList 待检测的绑定事件名称 (支持多个)
 * @return  是否绑定事件
 * @see https://stackoverflow.com/questions/46706737/check-if-a-component-has-an-event-listener-attached-to-it/76208995#76208995
 */
export function isEventExist(...eventNameList: string[]): boolean {
  // 输入验证
  if (!eventNameList.length) {
    return false
  }

  const currentInstance = getCurrentInstance()
  if (!currentInstance) {
    return false
  }

  const { attrs = {}, vnode } = currentInstance
  // 增加 vnode 空值检查
  if (!vnode) {
    return false
  }

  const props = vnode.props || {}

  const allEventKeys: string[] = [
    ...Object.keys(attrs).filter(key => key.startsWith(VUE_EVENT_PREFIX)),
    ...Object.keys(props).filter(key => key.startsWith(VUE_EVENT_PREFIX)),
  ];

  // 遍历查找事件
  for (const eventName of eventNameList) {
    const vueEventName = eventName.startsWith(VUE_EVENT_PREFIX)
      ? eventName
      : `${VUE_EVENT_PREFIX}${upperFirst(eventName)}`

    if (allEventKeys.includes(vueEventName)) {
      return true
    }

    // 检查带修饰符的事件
    for (const key of allEventKeys) {
      if (key.startsWith(vueEventName)) {
        const modifierPart = key.slice(vueEventName.length)
        if (modifierPart && EVENT_MODIFIER_REGEXP.test(modifierPart)) {
          return true
        }
      }
    }
  }

  return false
}
