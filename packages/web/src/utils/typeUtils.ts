/**
 * 检查值是否为函数
 * @param value 要检查的值
 * @returns 是否为函数
 */
export function isFunction(value: any): value is Function {
  return typeof value === 'function';
}

/**
 * 检查值是否为 null
 * @param value 要检查的值
 * @returns 是否为 null
 */
export function isNull(value: any): value is null {
  return value === null;
}

/**
 * 检查值是否为 undefined
 * @param value 要检查的值
 * @returns 是否为 undefined
 */
export function isUndefined(value: any): value is undefined {
  return value === undefined;
}
