import { defineConfig } from 'vite'
import dts from 'vite-plugin-dts'
import {fileURLToPath, URL} from "node:url";



export default defineConfig({
  build: {
    lib: {
      entry: 'src/index.ts',
      formats: ['es'],
      fileName: (format) => `index.${format}.js`,
    },
    sourcemap: false,
    outDir: 'dist',
    rollupOptions: {
      external: ['@vueuse/core', 'vue-router'],
    }
  },
  plugins: [
    dts({}),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})
