import type { Router } from 'vue-router'
import { isFunction } from 'es-toolkit';

export interface PluginEnterEvent {
  code: string;
  type: string;
  payload: any;
  option: any
}

export class UtoolsCodeEvent<T = Record<string, any>> extends CustomEvent<any> {
  public pluginEnterParams: PluginEnterEvent;
  public router: Router;
  public params: T = {} as T;

  private static PARAMS_SPLIT = "?";

  constructor(pluginEnterParams: PluginEnterEvent, router: Router) {
    super(pluginEnterParams.code.split(UtoolsCodeEvent.PARAMS_SPLIT)[0]);
    this.pluginEnterParams = pluginEnterParams;
    this.router = router;
    this.handleEventCode(pluginEnterParams);
  }

  public handleEventCode(data: PluginEnterEvent) {
    const { code, payload, type } = data;
    this.payloadParams(code);
    if (type === 'regex' && payload.toString().startsWith("#")) {
      this.payloadParams(payload.toString().substring(1));
    }
  }

  private payloadParams(code: string) {
    const codes = code.split(UtoolsCodeEvent.PARAMS_SPLIT)

    if (codes.length > 2) {
      throw Error(`code 错误 ${UtoolsCodeEvent.PARAMS_SPLIT} 仅可出现 1 或 0 次`)
    }

    if (codes.length > 1) {
      if (codes.includes('.')) {
        throw Error(`code 错误 ${UtoolsCodeEvent.PARAMS_SPLIT} 仅可以出现在.后面`)
      }
    }

    if (codes.length > 1 && codes[1].includes('=')) {
      const params = new URLSearchParams(codes[1])
      this.params = Array.from(params.entries()).reduce((acc, [key, value]) => {
        acc[key] = value as any
        return acc
      }, {} as any) as T
    } else {
      this.params = {
        [codes[1]]: true
      } as T
    }
  }

  public hasParamKey(key: string) {
    return Object.keys(this.params as any).includes(key);
  }

  public getParamsKey(key: string) {
    // @ts-ignore
    return this.params[key] as any;
  }
}


export interface PluginEnterEvent {
  code: string;
  type: string;
  payload: any;
  option: any
}


/**
 * 事件类型判断辅助函数
 * @param obj
 */
function isUtoolsCodeEventDetail(obj: Event): obj is UtoolsCodeEvent {
  return obj instanceof UtoolsCodeEvent
}

/**
 * 派发事件
 * @param data 数据
 * @param router 路由
 */
export function dispatchUtoolsCodeEvent(data: PluginEnterEvent, router: Router) {
  const event = new UtoolsCodeEvent(data, router)
  console.log('dispatchUtoolsCodeEvent', event)
  window.dispatchEvent(event)
}


/**
 * 添加监听 utools code 事件
 * @param type 事件类型
 * @param event 事件
 */
export function addUtoolsCodeEventListener(type: string, event: (e: UtoolsCodeEvent) => void) {
  window.addEventListener(type, (e) => {
    if (isUtoolsCodeEventDetail(e)) {
      event(e)
    }
  })
}

export interface InitBaseEventHandlerOptions {
  pluginHeight?: (() => number) | number;
}
const defaultInitBaseEventHandlerOptions = {
  pluginHeight: 600
}

export function initBaseEventHandler(options: InitBaseEventHandlerOptions = defaultInitBaseEventHandlerOptions) {
  utools.setExpendHeight(isFunction(options.pluginHeight) ? options.pluginHeight() : options.pluginHeight || 600);
  addUtoolsCodeEventListener('ui.router', (e) => {
    console.log('ui.router', e)
    e.router.replace({ name: e.getParamsKey('router'), query: { ...e.params } })
      .then(() => {});
    utools.setExpendHeight(600);
  });
}
