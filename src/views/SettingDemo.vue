<script setup lang="ts">
import { SettingGroup, SettingItem, SwitchEnable } from '@xiaou66/u-web-ui'
import { ref, watch } from 'vue'
const value = ref(false)

// 监听 value 变化
watch(value, (newVal) => {
  console.log('v-model value changed:', newVal)
})

function handleChange(v: boolean) {
  console.log('change event:', v)
}

function toggleValue() {
  value.value = !value.value
}

const value2 = ref('1')
</script>

<template>
  <div style="padding: 20px;">
    <h3>SwitchEnable 测试</h3>
    <p>当前值: {{ value }}</p>

    <SwitchEnable v-model:value="value"
                  @change="handleChange" />

    <br><br>
    <t-button @click="toggleValue">手动切换值</t-button>

    <br><br>
    <SettingGroup title="我是设置分组">
      <SettingItem title="我是设置" desc="我是设置描述">
        <t-input class="u-t-input"></t-input>
      </SettingItem>
    </SettingGroup>
    <t-radio-group class="u-web-radio-group"
                   v-model="value2"
                   variant="default-filled">
      <t-radio-button value="1">utools 截图</t-radio-button>
      <t-radio-button value="2">222</t-radio-button>
    </t-radio-group>
  </div>
</template>

<style lang="less">

</style>
