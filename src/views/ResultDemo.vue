<template>
  <div class="p-6 space-y-8">
    <t-select size="small" multiple filterable>
      <t-option :value="1" label="11">测试1</t-option>
      <t-option :value="2" label="22">测试2</t-option>
    </t-select>
    <h1 class="text-2xl font-bold mb-6">Result 组件演示</h1>

    <!-- 基础状态 -->
    <div class="space-y-6">
      <h2 class="text-lg font-semibold">基础状态</h2>

      <div class="grid grid-cols-2 gap-6">
        <div class="border rounded-lg p-4">
          <h3 class="text-sm font-medium mb-4">成功状态</h3>
          <Result
            status="success"
            title="操作成功"
            subtitle="您的操作已经成功完成"
          >
            <template #extra>
              <t-button theme="primary">返回首页</t-button>
            </template>
          </Result>
        </div>

        <div class="border rounded-lg p-4">
          <h3 class="text-sm font-medium mb-4">信息状态</h3>
          <Result
            status="info"
            title="信息提示"
            subtitle="这是一条信息提示"
          >
            <template #extra>
              <t-button>了解更多</t-button>
            </template>
          </Result>
        </div>

        <div class="border rounded-lg p-4">
          <h3 class="text-sm font-medium mb-4">警告状态</h3>
          <Result
            status="warning"
            title="警告提示"
            subtitle="请注意相关风险"
          >
            <template #extra>
              <t-button theme="warning">继续操作</t-button>
            </template>
          </Result>
        </div>

        <div class="border rounded-lg p-4">
          <h3 class="text-sm font-medium mb-4">错误状态</h3>
          <Result
            status="error"
            title="操作失败"
            subtitle="操作失败，请重试"
          >
            <template #extra>
              <t-button theme="danger">重试</t-button>
            </template>
          </Result>
        </div>
      </div>
    </div>

    <!-- 错误页面 -->
    <div class="space-y-6">
      <h2 class="text-lg font-semibold">错误页面</h2>

      <div class="grid grid-cols-1 gap-6">
        <div class="border rounded-lg p-4">
          <h3 class="text-sm font-medium mb-4">403 禁止访问</h3>
          <Result
            status="403"
            title="403 禁止访问"
            subtitle="抱歉，您没有权限访问此页面"
          >
            <template #extra>
              <t-space>
                <t-button>返回首页</t-button>
                <t-button theme="primary">申请权限</t-button>
              </t-space>
            </template>
          </Result>
        </div>

        <div class="border rounded-lg p-4">
          <h3 class="text-sm font-medium mb-4">404 页面不存在</h3>
          <Result
            status="404"
            title="404 页面不存在"
            subtitle="抱歉，您访问的页面不存在"
          >
            <template #extra>
              <t-space>
                <t-button>返回首页</t-button>
                <t-button theme="primary">重新搜索</t-button>
              </t-space>
            </template>
          </Result>
        </div>

        <div class="border rounded-lg p-4">
          <h3 class="text-sm font-medium mb-4">500 服务器错误</h3>
          <Result
            status="500"
            title="500 服务器错误"
            subtitle="服务器出现错误，请稍后重试"
          >
            <template #extra>
              <t-space>
                <t-button>返回首页</t-button>
                <t-button theme="primary">刷新页面</t-button>
              </t-space>
            </template>
          </Result>
        </div>
      </div>
    </div>

    <!-- 自定义图标 -->
    <div class="space-y-6">
      <h2 class="text-lg font-semibold">自定义图标</h2>

      <div class="border rounded-lg p-4">
        <h3 class="text-sm font-medium mb-4">自定义图标</h3>
        <Result
          status="null"
          title="自定义结果"
          subtitle="使用自定义图标的结果页面"
        >
          <template #icon>
            <span class="i-p-loading text-6xl text-blue-500" />
          </template>
          <template #extra>
            <t-button theme="primary">确定</t-button>
          </template>
        </Result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Result } from '@xiaou66/u-web-ui'
</script>

<style scoped lang="less">
.space-y-8 > * + * {
  margin-top: 2rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}
</style>
