<script setup lang="ts">
import { PageHeader } from '@xiaou66/u-web-ui'

const handleBack = () => {
  console.log('Back button clicked!');
  alert('返回按钮被点击了！');
};
</script>

<template>
  <div class="page-header-demo">
<!--    <h2>PageHeader Component Demo</h2>-->

<!--    <div class="demo-section">-->
<!--      <h3>Custom Title</h3>-->
<!--      <PageHeader-->
<!--        title="设置页面"-->
<!--        subtitle="subtitle"-->
<!--        size="small"-->
<!--      />-->
<!--    </div>-->
<!--    <div class="demo-section">-->
<!--      <h3>Custom Title</h3>-->
<!--      <PageHeader-->
<!--        title="设置页面"-->
<!--        subtitle="subtitle"-->
<!--        size="default"-->
<!--      />-->
<!--    </div>-->
<!--    <div class="demo-section">-->
<!--      <h3>Custom Title</h3>-->
<!--      <PageHeader-->
<!--        title="设置页面"-->
<!--        subtitle="subtitle"-->
<!--        size="large"-->
<!--      />-->
<!--    </div>-->

<!--    <div class="demo-section">-->
<!--      <h3>带返回按钮的 PageHeader</h3>-->
<!--      <PageHeader-->
<!--        title="设置页面"-->
<!--        subtitle="subtitle"-->
<!--        size="default"-->
<!--        @back="handleBack"-->
<!--      />-->
<!--    </div>-->
<!--    <div class="demo-section">-->
<!--      <h3>Custom Title</h3>-->
<!--      <PageHeader-->
<!--        title="设置页面"-->
<!--        subtitle="subtitle"-->
<!--        size="large"-->
<!--      >-->
<!--        <template #extra>-->
<!--          <t-button>帮助</t-button>-->
<!--        </template>-->
<!--      </PageHeader>-->
<!--    </div>-->
    <div class="demo-section">
    <PageHeader
      title="设置页面"
      subtitle="subtitle"
      size="small"
    >
      <template #extra>
        <t-button>帮助</t-button>
      </template>
    </PageHeader>
      <div style="width: 1200px"></div>
    </div>
    <t-divider class="u-web-divider-mini">111</t-divider>
  </div>
</template>

<style scoped lang="less">

</style>
