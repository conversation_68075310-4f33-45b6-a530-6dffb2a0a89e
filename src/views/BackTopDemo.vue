<script setup lang="ts">

import { BackTop } from "@xiaou66/u-web-ui";

const handleBackTopClick = () => {
  console.log('回到顶部按钮被点击了！')
}
</script>

<template>
  <div class="demo-container">
    <h1>BackTop 回到顶部组件演示</h1>

    <div class="demo-section">
      <h2>基础用法</h2>
      <p>滚动页面到一定高度后，右下角会出现回到顶部按钮</p>

      <!-- 创建足够的内容来触发滚动 -->
      <div class="content">
        <div v-for="i in 50" :key="i" class="content-item">
          <h3>内容区块 {{ i }}</h3>
          <p>这是第 {{ i }} 个内容区块。当页面滚动超过 200px 时，回到顶部按钮会出现在右下角。</p>
          <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2>自定义触发高度</h2>
      <p>可以通过 visibleHeight 属性自定义触发显示的滚动高度</p>
    </div>

    <div class="demo-section">
      <h2>自定义内容</h2>
      <p>可以通过插槽自定义按钮内容</p>
    </div>

    <!-- 默认回到顶部按钮 -->
    <BackTop size="small" target-container=".content-inner" @click="handleBackTopClick" />
  </div>
</template>

<style scoped>
.demo-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  min-height: 2000px; /* 确保页面有足够高度来测试滚动 */
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e5e6ec;
  border-radius: 8px;
}

.content-item {
  padding: 20px;
  margin-bottom: 20px;
  background-color: #f7f8fa;
  border-radius: 6px;
}

.content-item h3 {
  margin: 0 0 10px 0;
  color: #1d2129;
}

.content-item p {
  margin: 0 0 10px 0;
  line-height: 1.6;
  color: #4e5969;
}

.custom-back-top span {
  font-size: 12px;
}
</style>


