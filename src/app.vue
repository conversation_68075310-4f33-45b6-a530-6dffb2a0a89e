<script setup lang="ts">
import {onMounted} from 'vue'
import { UtoolsLayout } from '@xiaou66/u-web-ui'
import { routes } from './router'
onMounted(() => {
  // document.documentElement.setAttribute("theme-mode", "dark");
})

function handleLoadRouter() {
  return routes;
}
</script>

<template>
  <UtoolsLayout :load-router="handleLoadRouter"
                size="small"></UtoolsLayout>
</template>

<style lang="less">
body {
  overflow: hidden;
}
</style>
