# u-package

This template should help get you started developing with Vue 3 in Vite.

## 📋 Requirements

This project has strict requirements for Node.js version and package manager:

- **Node.js**: >= 20.0.0
- **Package Manager**: pnpm only (npm and yarn are not allowed)

### Why these restrictions?

- **Node.js >= 20**: Ensures access to the latest features, performance improvements, and security updates
- **pnpm only**: Provides better disk space efficiency, faster installations, and stricter dependency management in monorepo environments

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

⚠️ **Important**: This project only supports pnpm. Using npm or yarn will fail.

```sh
# Install dependencies
pnpm install

# If you don't have pnpm installed globally:
npm install -g pnpm
```

### Compile and Hot-Reload for Development

```sh
pnpm run dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm run build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
pnpm run test:unit
```

### Run End-to-End Tests with [Playwright](https://playwright.dev)

```sh
# Install browsers for the first run
npx playwright install

# When testing on CI, must build the project first
pnpm run build

# Runs the end-to-end tests
pnpm run test:e2e
# Runs the tests only on Chromium
pnpm run test:e2e -- --project=chromium
# Runs the tests of a specific file
pnpm run test:e2e -- tests/example.spec.ts
# Runs the tests in debug mode
pnpm run test:e2e -- --debug
```

### Lint with [ESLint](https://eslint.org/)

```sh
pnpm run lint
```

## 🚫 What happens if you try to use npm or yarn?

If you attempt to use `npm install` or `yarn install`, you'll see an error message like:

```
Use "pnpm install" for installation in this project
```

This is enforced by the `preinstall` script in package.json and the `only-allow` package.
